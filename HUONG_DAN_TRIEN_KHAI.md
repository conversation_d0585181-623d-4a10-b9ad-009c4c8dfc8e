# 🎯 HƯỚNG DẪN TRIỂN KHAI HỆ THỐNG BÓNG ĐÁ TRÊN UBUNTU 22.04

## 📋 Tổng quan hệ thống

**Hệ thống Bóng Đá** là một ứng dụng web quản lý cá cược bóng đá với các thành phần:

- **Backend**: Laravel 5.4 (PHP) - API và quản lý admin
- **Crawl Service**: Node.js - Thu thập dữ liệu trận đấu
- **Database**: PostgreSQL - Lưu trữ dữ liệu
- **Cache**: Redis - Cache và session
- **Web Server**: Nginx - Reverse proxy và static files
- **Container**: Docker & Docker Compose

---

## 🔧 BƯỚC 1: CÀI ĐẶT DEPENDENCIES

### 1.1 Cập nhật hệ thống
```bash
sudo apt update && sudo apt upgrade -y
```

### 1.2 Cài đặt Docker
```bash
# Cài đặt các g<PERSON><PERSON> cần thiết
sudo apt install apt-transport-https ca-certificates curl software-properties-common -y

# Thêm Docker GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# Thêm Docker repository
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Cài đặt Docker
sudo apt update
sudo apt install docker-ce docker-ce-cli containerd.io -y
```

### 1.3 Cài đặt Docker Compose
```bash
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Thêm user vào group docker
sudo usermod -aG docker $USER

# Logout và login lại để áp dụng thay đổi
```

---

## 📁 BƯỚC 2: CHUẨN BỊ SOURCE CODE

### 2.1 Tạo thư mục dự án
```bash
sudo mkdir -p /var/www/bong88
sudo chown $USER:$USER /var/www/bong88
cd /var/www/bong88
```

### 2.2 Upload source code
```bash
# Sử dụng SCP để upload
scp -r ./home/<USER>/var/www/bong88/

# Hoặc sử dụng rsync
rsync -avz ./home/<USER>/var/www/bong88/

# Hoặc clone từ git repository
git clone <repository-url> .
```

### 2.3 Phân quyền files
```bash
cd /var/www/bong88/home
sudo chown -R $USER:$USER .
sudo chmod -R 755 .
sudo chmod -R 777 backend/storage
sudo chmod -R 777 backend/bootstrap/cache
```

---

## 🌐 BƯỚC 3: CÀI ĐẶT VÀ CÂU HÌNH NGINX

### 3.1 Cài đặt Nginx
```bash
sudo apt install nginx -y
sudo systemctl enable nginx
sudo systemctl start nginx
```

### 3.2 Cấu hình domain
```bash
# Tạo file cấu hình cho domain
sudo nano /etc/nginx/sites-available/bong88.conf
```

**Nội dung file cấu hình:**
```nginx
server {
    listen 80;
    server_name 88zon.icu www.88zon.icu;
    
    # Redirect HTTP to HTTPS (sau khi cài SSL)
    # return 301 https://$server_name$request_uri;
    
    location / {
        proxy_pass http://localhost:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

### 3.3 Kích hoạt site
```bash
# Tạo symbolic link
sudo ln -s /etc/nginx/sites-available/bong88.conf /etc/nginx/sites-enabled/

# Xóa default site
sudo rm /etc/nginx/sites-enabled/default

# Test cấu hình
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

---

## 🐳 BƯỚC 4: CHẠY ỨNG DỤNG VỚI DOCKER

### 4.1 Cập nhật cấu hình domain trong container
```bash
cd /var/www/bong88/home
nano nginx/conf.d/app.conf
```

**Thay đổi các domain trong file:**
- Thay `bb8ag.net ag8bong88.net` thành `your-domain.com www.your-domain.com`
- Thay `m.demotes001.site m.8viva88.net` thành `m.your-domain.com`
- Thay `demotes001.site 8viva88.net` thành `api.your-domain.com`

### 4.2 Tạo Docker network
```bash
docker network create api-system
```

### 4.3 Build và chạy containers
```bash
cd /var/www/bong88/home

# Build và chạy tất cả services
docker-compose up -d --build

# Kiểm tra trạng thái containers
docker-compose ps
```

### 4.4 Kiểm tra logs
```bash
# Xem logs tất cả services
docker-compose logs -f

# Xem logs từng service
docker logs backend
docker logs nginx
docker logs crawl
docker logs postgres
docker logs redis
```

---

## ⚙️ BƯỚC 5: CẤU HÌNH LARAVEL

### 5.1 Cài đặt dependencies và migrate database
```bash
# Vào container backend
docker exec -it backend bash

# Trong container, chạy:
composer install --no-dev --optimize-autoloader
php artisan key:generate
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Migrate database và seed data
php artisan migrate
php artisan db:seed

# Thoát container
exit
```

### 5.2 Cấu hình permissions
```bash
# Set permissions cho Laravel
docker exec -it backend bash -c "chown -R www-data:www-data /var/www/storage /var/www/bootstrap/cache"
docker exec -it backend bash -c "chmod -R 775 /var/www/storage /var/www/bootstrap/cache"
```

---

## 🔐 BƯỚC 6: THÔNG TIN ĐĂNG NHẬP ADMIN

### 6.1 Tài khoản Admin mặc định

| Loại tài khoản | Username | Password | Mô tả |
|----------------|----------|----------|-------|
| **Super Admin** | `ADMIN` | `bongda@123` | Quyền cao nhất |
| **Super** | `B0` | `123456` | Quản lý cấp cao |
| **Master** | `B000` | `123456` | Quản lý trung gian |
| **Agent** | `B00000` | `123456` | Đại lý |
| **Member** | `B0000000` | `123456` | Thành viên |

### 6.2 URL đăng nhập
```
http://your-domain.com/portal/login
```

### 6.3 Cấu trúc phân quyền
- **ADMIN**: Toàn quyền hệ thống, quản lý users, cấu hình
- **SUPER**: Quản lý Master và Agent
- **MASTER**: Quản lý Agent và Member
- **AGENT**: Quản lý Member, tạo tài khoản
- **MEMBER**: Người dùng cuối, đặt cược

---

## 🎛️ BƯỚC 7: GIAO DIỆN ADMIN

### 7.1 Trang chủ Admin
- **Dashboard**: Thống kê tổng quan
- **Quản lý Users**: Danh sách, thêm/sửa/xóa users
- **Quản lý Bets**: Xem và quản lý cược
- **Quản lý Events**: Quản lý sự kiện thể thao
- **Báo cáo**: Thống kê win/lose, doanh thu

### 7.2 Menu chính
```
├── Home (Trang chủ)
├── Members (Quản lý thành viên)
├── Bets Management (Quản lý cược)
│   ├── Running Bets (Cược đang chạy)
│   ├── Pending Bets (Cược chờ xử lý)
│   └── All Bets (Tất cả cược)
├── Events (Quản lý sự kiện)
├── Reports (Báo cáo)
├── Settings (Cài đặt)
└── Logout (Đăng xuất)
```

### 7.3 Chức năng chính
- **Quản lý tài khoản**: Tạo, sửa, khóa/mở khóa user
- **Quản lý ví**: Nạp/rút tiền, xem lịch sử giao dịch
- **Quản lý cược**: Xem, hủy, chỉnh sửa cược
- **Báo cáo**: Win/Lose, doanh thu theo thời gian
- **Cài đặt hệ thống**: Cấu hình tỷ lệ, giới hạn cược

---

## 🔒 BƯỚC 8: CÀI ĐẶT SSL (TÙY CHỌN)

### 8.1 Cài đặt Certbot
```bash
sudo apt install certbot python3-certbot-nginx -y
```

### 8.2 Tạo SSL certificate
```bash
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

### 8.3 Test auto-renewal
```bash
sudo certbot renew --dry-run
```

---

## 🛠️ BƯỚC 9: SCRIPT QUẢN LÝ

### 9.1 Tạo script quản lý
```bash
sudo nano /usr/local/bin/bong88-manage
```

**Nội dung script:**
```bash
#!/bin/bash
cd /var/www/bong88/home

case "$1" in
    start)
        docker-compose up -d
        echo "✅ Bong88 started"
        ;;
    stop)
        docker-compose down
        echo "⏹️ Bong88 stopped"
        ;;
    restart)
        docker-compose restart
        echo "🔄 Bong88 restarted"
        ;;
    logs)
        docker-compose logs -f
        ;;
    status)
        docker-compose ps
        ;;
    backup)
        docker exec postgres pg_dump -U postgres bongda > backup_$(date +%Y%m%d_%H%M%S).sql
        echo "💾 Database backed up"
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|logs|status|backup}"
        exit 1
        ;;
esac
```

### 9.2 Cấp quyền thực thi
```bash
sudo chmod +x /usr/local/bin/bong88-manage
```

### 9.3 Sử dụng script
```bash
bong88-manage start    # Khởi động
bong88-manage stop     # Dừng
bong88-manage restart  # Khởi động lại
bong88-manage logs     # Xem logs
bong88-manage status   # Kiểm tra trạng thái
bong88-manage backup   # Backup database
```

---

## 🚀 BƯỚC 10: TỰ ĐỘNG KHỞI ĐỘNG

### 10.1 Tạo systemd service
```bash
sudo nano /etc/systemd/system/bong88.service
```

**Nội dung service:**
```ini
[Unit]
Description=Bong88 Application
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/var/www/bong88/home
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
```

### 10.2 Kích hoạt service
```bash
sudo systemctl enable bong88.service
sudo systemctl start bong88.service
```

---

## 📊 BƯỚC 11: MONITORING VÀ MAINTENANCE

### 11.1 Kiểm tra hệ thống
```bash
# Kiểm tra containers
docker ps

# Kiểm tra resource usage
docker stats

# Kiểm tra disk space
df -h

# Kiểm tra logs
tail -f /var/log/nginx/error.log
```

### 11.2 Backup định kỳ
```bash
# Tạo cron job backup hàng ngày
crontab -e

# Thêm dòng sau (backup lúc 2:00 AM hàng ngày)
0 2 * * * /usr/local/bin/bong88-manage backup
```

### 11.3 Update hệ thống
```bash
cd /var/www/bong88/home

# Pull latest images
docker-compose pull

# Rebuild và restart
docker-compose up -d --build
```

---

## 🔧 KHẮC PHỤC LỖI PHỔ BIẾN

### Lỗi Docker Build - Debian Buster Repository
Nếu gặp lỗi `404 Not Found` khi build Docker image, đây là do Debian Buster đã ngừng hỗ trợ. Khắc phục bằng cách:

```bash
# 1. Cập nhật Dockerfile của webapp (app.Dockerfile)
nano /var/www/bong88/backend/app.Dockerfile
```

**Thay đổi FROM:**
```dockerfile
# Từ: FROM php:7.2-fpm
# Thành:
FROM php:7.4-fpm

# Hoặc sử dụng image cũ với archive repository:
FROM php:7.2-fpm
RUN sed -i 's/deb.debian.org/archive.debian.org/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/archive.debian.org/g' /etc/apt/sources.list && \
    sed -i '/stretch-updates/d' /etc/apt/sources.list
```

### Lỗi crawl.Dockerfile không tìm thấy
```bash
# Copy file crawl.Dockerfile về đúng vị trí
cp /var/www/bong88/crawl/crawl.Dockerfile /var/www/bong88/backend/

# Hoặc tạo symbolic link
cd /var/www/bong88/backend
ln -s ../crawl/crawl.Dockerfile crawl.Dockerfile
```

### Lỗi kết nối Database
```bash
# Kiểm tra và sửa file .env
nano /var/www/bong88/backend/.env

# Đảm bảo có các thông tin:
DB_CONNECTION=pgsql
DB_HOST=db
DB_PORT=5432
DB_DATABASE=bongda
DB_USERNAME=postgres
DB_PASSWORD=your_password
```

---

## ⚠️ LƯU Ý QUAN TRỌNG

### 🔐 Bảo mật
1. **Đổi password mặc định** ngay sau khi cài đặt
2. **Cấu hình firewall** chỉ mở port cần thiết (80, 443, 22)
3. **Backup định kỳ** database và files quan trọng
4. **Update thường xuyên** hệ thống và dependencies

### 🌐 Domain và DNS
1. **Trỏ domain** về IP của VPS
2. **Cấu hình subdomain** cho mobile (m.domain.com)
3. **Cài đặt SSL** cho bảo mật

### 📈 Performance
1. **Monitor resource usage** thường xuyên
2. **Optimize database** khi cần thiết
3. **Scale containers** khi traffic tăng cao

### 🔧 Troubleshooting
```bash
# Kiểm tra logs khi có lỗi
docker-compose logs backend
docker-compose logs nginx
docker-compose logs postgres

# Restart service khi cần
bong88-manage restart

# Kiểm tra port đang sử dụng
netstat -tulpn | grep :80
```

---

## 📞 HỖ TRỢ

Nếu gặp vấn đề trong quá trình triển khai, hãy kiểm tra:

1. **Logs của containers**: `docker-compose logs`
2. **Trạng thái services**: `docker-compose ps`
3. **Kết nối database**: Kiểm tra credentials trong `.env`
4. **DNS và domain**: Đảm bảo domain đã trỏ đúng IP
5. **Firewall**: Kiểm tra port 80, 443 đã mở

**Chúc bạn triển khai thành công! 🎉**
