# 🏆 Hướng Dẫn Triển <PERSON>hai <PERSON>ệ <PERSON>hống Bóng Đá BONG88

## 📋 Mục <PERSON>
1. [Giớ<PERSON> Thiệu <PERSON>ố<PERSON>](#giới-thiệu-hệ-thống)
2. [<PERSON><PERSON><PERSON>](#yêu-cầu-hệ-thống)
3. [<PERSON>à<PERSON> Đặt Trên VPS Ubuntu 22.04](#cài-đặt-trên-vps-ubuntu-2204)
4. [<PERSON><PERSON><PERSON> <PERSON><PERSON> Trườ<PERSON>](#cấu-hình-môi-trường)
5. [Triển <PERSON>ố<PERSON>](#triển-khai-hệ-thống)
6. [Thông Tin Đăng Nhập](#thông-tin-đăng-nhập)
7. [Quản <PERSON>ố<PERSON>](#quản-lý-hệ-thống)
8. [Troubleshooting](#troubleshooting)
9. [<PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON> Ưu](#bảo-mật--tối-ưu)

---

## 🎯 Giới Thiệu Hệ Thống

### Kiến Trúc Hệ Thống
Hệ thống BONG88 bao gồm các thành phần chính:

- **🖥️ Backend API**: Laravel 5.4 (PHP)
- **🕷️ Crawl Service**: Node.js (Thu thập dữ liệu)
- **🗄️ Database**: PostgreSQL 10.1
- **🚀 Cache**: Redis
- **🌐 Web Server**: Nginx
- **📱 Frontend**: Mobile Web App
- **🐳 Container**: Docker & Docker Compose

### Chức Năng Chính
- Quản lý cá cược bóng đá
- Thu thập dữ liệu tỷ lệ cược
- Hệ thống user đa cấp (Admin > Super > Master > Agent > Member)
- API RESTful
- Mobile responsive

---

## 💻 Yêu Cầu Hệ Thống

### VPS Tối Thiểu
- **OS**: Ubuntu 22.04 LTS
- **RAM**: 4GB (khuyến nghị 8GB)
- **Storage**: 50GB SSD
- **CPU**: 2 cores
- **Network**: 100Mbps

### Phần Mềm Cần Thiết
- Docker 20.x+
- Docker Compose 2.x+
- Git
- Curl/Wget

---

## 🚀 Cài Đặt Trên VPS Ubuntu 22.04

### Bước 1: Cập Nhật Hệ Thống

```bash
# Đăng nhập VPS và cập nhật
sudo apt update && sudo apt upgrade -y

# Cài đặt các gói cần thiết
sudo apt install -y curl wget git unzip software-properties-common
```

### Bước 2: Cài Đặt Docker

```bash
# Gỡ bỏ các phiên bản Docker cũ (nếu có)
sudo apt remove docker docker-engine docker.io containerd runc

# Thêm Docker repository
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Cài đặt Docker
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io

# Cài đặt Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Thêm user vào group docker
sudo usermod -aG docker $USER

# Khởi động và enable Docker
sudo systemctl start docker
sudo systemctl enable docker

# Kiểm tra cài đặt
docker --version
docker-compose --version
```

### Bước 3: Tạo Thư Mục Dự Án

```bash
# Tạo thư mục chính
sudo mkdir -p /var/www/bong88
sudo chown -R $USER:$USER /var/www/bong88
cd /var/www/bong88
```

---

## 📁 Cấu Hình Môi Trường

### Bước 1: Kiểm Tra Source Code Đã Upload

```bash
# Di chuyển vào thư mục dự án trên VPS
cd /var/www/bong88

# Kiểm tra cấu trúc thư mục hiện tại
echo "🔍 Kiểm tra cấu trúc thư mục hiện tại:"
ls -la

# Kiểm tra thư mục backend (quan trọng nhất)
echo "📁 Kiểm tra thư mục backend:"
ls -la backend/

# Kiểm tra các file quan trọng trong backend
echo "🔍 Kiểm tra các file Laravel quan trọng:"
echo "✓ artisan: $([ -f "backend/artisan" ] && echo "✅ OK" || echo "❌ Missing")"
echo "✓ composer.json: $([ -f "backend/composer.json" ] && echo "✅ OK" || echo "❌ Missing")"
echo "✓ .env.example: $([ -f "backend/.env.example" ] && echo "✅ OK" || echo "❌ Missing")"
echo "✓ app/: $([ -d "backend/app" ] && echo "✅ OK" || echo "❌ Missing")"
echo "✓ public/: $([ -d "backend/public" ] && echo "✅ OK" || echo "❌ Missing")"
echo "✓ config/: $([ -d "backend/config" ] && echo "✅ OK" || echo "❌ Missing")"

# Kiểm tra các thư mục khác
echo ""
echo "🔍 Kiểm tra các thành phần khác:"
for dir in crawl nginx postgresql; do
    if [ -d "$dir" ]; then
        echo "✅ $dir/ - Đã có ($(ls $dir 2>/dev/null | wc -l) files)"
    else
        echo "❌ $dir/ - Thiếu"
    fi
done

# Kiểm tra file docker-compose.yml
echo "� Kiểm tra Docker Compose:"
if [ -f "docker-compose.yml" ]; then
    echo "✅ docker-compose.yml - Đã có"
    echo "� Nội dung docker-compose.yml:"
    head -20 docker-compose.yml
else
    echo "❌ docker-compose.yml - Thiếu (sẽ tạo sau)"
fi

# Kiểm tra dung lượng sử dụng
echo ""
echo "� Dung lượng sử dụng:"
du -sh . 2>/dev/null
du -sh */ 2>/dev/null | sort -hr | head -10

echo ""
echo "🎯 Trạng thái triển khai:"
if [ -d "backend" ] && [ -f "backend/artisan" ]; then
    echo "✅ SẴN SÀNG TRIỂN KHAI"
    echo "📋 Các bước tiếp theo:"
    echo "   1. Cấu hình file .env"
    echo "   2. Tạo cấu hình nginx"
    echo "   3. Khởi động Docker containers"
else
    echo "⚠️ CẦN KIỂM TRA SOURCE CODE"
fi
```

### Bước 2: Tạo File .env Từ Backend

```bash
# Kiểm tra file .env.example trong thư mục backend
if [ -f "backend/.env.example" ]; then
    echo "✅ Tìm thấy .env.example trong backend/"
    # Copy .env.example ra ngoài thư mục gốc
    cp backend/.env.example .env
    echo "📋 Đã copy .env.example -> .env"
else
    echo "⚠️ Không tìm thấy .env.example, tạo file .env mới"
    # Tạo file .env mới nếu không có
    cat > .env << 'ENVEOF'
APP_NAME=BONG88
APP_ENV=production
APP_KEY=base64:DVrVwYSL6z2KSwCpyeu50j5Gkhi2Cgjj2gjL0UMO0l0=
APP_DEBUG=false
APP_URL=http://your-domain.com

DB_CONNECTION=pgsql
DB_HOST=db
DB_PORT=5432
DB_DATABASE=bongda
DB_USERNAME=postgres
DB_PASSWORD=uN7s2P89cr3kV

REDIS_HOST=redis
REDIS_PASSWORD=RWzf@(V@Hps1+igQ5
REDIS_PORT=6379

APP_API=https://api.betsapi.com
APP_API_TOKEN=3869-hDrKmu1jsNQ3TC
JWT_SECRET=DVrVwYSL6z2KSwCpyeu50j5Gkhi2Cgjj2gjL0UMO0l0
ENVEOF
fi

# Kiểm tra file .env đã được tạo
if [ -f ".env" ]; then
    echo "✅ File .env đã được tạo thành công"
    echo "� Vị trí: $(pwd)/.env"
    echo ""
    echo "🔧 Nội dung file .env (ẩn password):"
    cat .env | grep -v PASSWORD | grep -v SECRET | head -10
else
    echo "❌ Không thể tạo file .env"
fi
```

### Bước 3: Tạo Docker Compose Configuration

```bash
# Kiểm tra nếu có file docker-compose.yml trong thư mục
if [ ! -f "docker-compose.yml" ]; then
    echo "� Tạo file docker-compose.yml..."
    
    cat > docker-compose.yml << 'DOCKEREOF'
version: '3.8'

services:
  backend:
    container_name: backend
    build:
      context: ./backend
      dockerfile: app.Dockerfile
    restart: unless-stopped
    env_file:
      - ./.env
    working_dir: /var/www
    volumes:
      - ./backend/:/var/www
      - backend_storage:/var/www/storage
    networks:
      - external
    depends_on:
      - db
      - redis

  db:
    container_name: postgres
    image: postgres:10.1-alpine
    restart: unless-stopped
    environment:
      POSTGRES_PASSWORD: uN7s2P89cr3kV
      POSTGRES_USER: postgres
      POSTGRES_DB: bongda
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - external

  nginx:
    container_name: nginx
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./backend/:/var/www
      - ./nginx/conf.d/:/etc/nginx/conf.d/
    networks:
      - external
    depends_on:
      - backend

  redis:
    container_name: redis
    image: redis:alpine
    restart: unless-stopped
    command: redis-server --requirepass "RWzf@(V@Hps1+igQ5"
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - external

  crawl:
    container_name: crawl
    build:
      context: ./crawl
      dockerfile: Dockerfile
    restart: unless-stopped
    env_file:
      - ./.env
    networks:
      - external
    depends_on:
      - db
      - redis

networks:
  external:
    name: api-system

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_storage:
    driver: local
DOCKEREOF

    echo "✅ File docker-compose.yml đã được tạo"
else
    echo "✅ File docker-compose.yml đã tồn tại"
fi

# Validate docker-compose file
echo "🧪 Kiểm tra cấu hình Docker Compose:"
if docker-compose config >/dev/null 2>&1; then
    echo "✅ Docker Compose configuration OK"
else
    echo "❌ Docker Compose configuration có lỗi:"
    docker-compose config
fi
```

### Bước 4: Chuẩn Bị Dockerfile cho Backend

```bash
# Kiểm tra Dockerfile trong backend
if [ ! -f "backend/app.Dockerfile" ] && [ ! -f "backend/Dockerfile" ]; then
    echo "� Tạo Dockerfile cho backend..."
    
    cat > backend/app.Dockerfile << 'DOCKERFILEEOF'
FROM php:7.4-fpm

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libpq-dev \
    zip \
    unzip \
    && docker-php-ext-configure pgsql -with-pgsql=/usr/local/pgsql \
    && docker-php-ext-install pdo pdo_pgsql pgsql mbstring exif pcntl bcmath gd

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www

# Copy application files
COPY . .

# Install PHP dependencies
RUN composer install --no-dev --optimize-autoloader

# Set permissions
RUN chown -R www-data:www-data /var/www/storage /var/www/bootstrap/cache
RUN chmod -R 775 /var/www/storage /var/www/bootstrap/cache

# Expose port 9000
EXPOSE 9000

CMD ["php-fpm"]
DOCKERFILEEOF

    echo "✅ Dockerfile đã được tạo: backend/app.Dockerfile"
else
    echo "✅ Dockerfile đã tồn tại trong backend/"
fi
```

### Bước 5: Tạo Cấu Hình Nginx

```bash
# Tạo thư mục nginx/conf.d nếu chưa có
mkdir -p nginx/conf.d

# Tạo file cấu hình nginx đơn giản
cat > nginx/conf.d/default.conf << 'NGINXEOF'
server {
    listen 80;
    server_name localhost;
    root /var/www/public;
    index index.php index.html;

    # Logs
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # Handle requests
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Handle API requests
    location /api/ {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP processing
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass backend:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        include fastcgi_params;
        
        # Timeout settings
        fastcgi_connect_timeout 60;
        fastcgi_send_timeout 60;
        fastcgi_read_timeout 60;
    }

    # Static files
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Security
    location ~ /\. {
        deny all;
    }

    location ~ /(storage|bootstrap|config|database|vendor)/ {
        deny all;
    }

    # Client settings
    client_max_body_size 50M;
}
NGINXEOF

echo "✅ Nginx configuration đã được tạo: nginx/conf.d/default.conf"
```

### 🔍 Script Kiểm Tra Tổng Thể

```bash
# Tạo script kiểm tra toàn bộ setup
cat > check_setup.sh << 'CHECKEOF'
#!/bin/bash

echo "🔍 KIỂM TRA SETUP TRIỂN KHAI BONG88"
echo "=================================="

echo "📁 1. Kiểm tra cấu trúc thư mục:"
echo "   Backend: $([ -d "backend" ] && echo "✅" || echo "❌")"
echo "   Crawl: $([ -d "crawl" ] && echo "✅" || echo "❌")"
echo "   Nginx: $([ -d "nginx" ] && echo "✅" || echo "❌")"

echo ""
echo "📄 2. Kiểm tra files cấu hình:"
echo "   .env: $([ -f ".env" ] && echo "✅" || echo "❌")"
echo "   docker-compose.yml: $([ -f "docker-compose.yml" ] && echo "✅" || echo "❌")"
echo "   backend/app.Dockerfile: $([ -f "backend/app.Dockerfile" ] && echo "✅" || echo "❌")"
echo "   nginx/conf.d/default.conf: $([ -f "nginx/conf.d/default.conf" ] && echo "✅" || echo "❌")"

echo ""
echo "🔧 3. Kiểm tra Laravel backend:"
echo "   artisan: $([ -f "backend/artisan" ] && echo "✅" || echo "❌")"
echo "   composer.json: $([ -f "backend/composer.json" ] && echo "✅" || echo "❌")"
echo "   app/ directory: $([ -d "backend/app" ] && echo "✅" || echo "❌")"
echo "   public/ directory: $([ -d "backend/public" ] && echo "✅" || echo "❌")"

echo ""
echo "🐳 4. Kiểm tra Docker:"
if command -v docker >/dev/null 2>&1; then
    echo "   Docker: ✅ $(docker --version)"
else
    echo "   Docker: ❌ Chưa cài đặt"
fi

if command -v docker-compose >/dev/null 2>&1; then
    echo "   Docker Compose: ✅ $(docker-compose --version)"
else
    echo "   Docker Compose: ❌ Chưa cài đặt"
fi

echo ""
echo "🌐 5. Kiểm tra cấu hình mạng:"
echo "   Port 8080 (nginx): $(sudo netstat -tlnp | grep :8080 >/dev/null 2>&1 && echo "⚠️ Đang sử dụng" || echo "✅ Trống")"
echo "   Port 5432 (postgres): $(sudo netstat -tlnp | grep :5432 >/dev/null 2>&1 && echo "⚠️ Đang sử dụng" || echo "✅ Trống")"
echo "   Port 6379 (redis): $(sudo netstat -tlnp | grep :6379 >/dev/null 2>&1 && echo "⚠️ Đang sử dụng" || echo "✅ Trống")"

echo ""
echo "💾 6. Dung lượng đĩa:"
echo "   Thư mục hiện tại: $(du -sh . 2>/dev/null | cut -f1)"
echo "   Dung lượng trống: $(df -h . | tail -1 | awk '{print $4}') available"

echo ""
echo "🎯 7. Trạng thái tổng thể:"
if [ -d "backend" ] && [ -f ".env" ] && [ -f "docker-compose.yml" ]; then
    echo "   ✅ SẴN SÀNG TRIỂN KHAI!"
    echo ""
    echo "� Các lệnh tiếp theo:"
    echo "   1. docker-compose build"
    echo "   2. docker-compose up -d"
    echo "   3. docker-compose exec backend php artisan migrate"
    echo "   4. docker-compose exec backend php artisan db:seed"
else
    echo "   ⚠️ CẦN HOÀN THIỆN CẤU HÌNH"
fi
CHECKEOF

chmod +x check_setup.sh
echo "✅ Script kiểm tra đã được tạo: check_setup.sh"

# Chạy script kiểm tra
echo ""
echo "🏃 Chạy kiểm tra setup:"
./check_setup.sh
```

### 🔍 Kiểm Tra Sau Khi Giải Nén

Chạy script kiểm tra để đảm bảo mọi thứ đã được giải nén đúng cách:

```bash
# Script kiểm tra tổng thể
cat > check_extraction.sh << 'EOF'
#!/bin/bash
echo "🔍 KIỂM TRA SAU KHI GIẢI NÉN"
echo "================================"

# Kiểm tra cấu trúc thư mục chính
echo "📁 Cấu trúc thư mục chính:"
ls -la

echo ""
echo "📊 Thống kê các thành phần:"

# Backend check
if [ -d "backend" ]; then
    backend_files=$(find backend -type f | wc -l)
    echo "✅ Backend: $backend_files files"
    echo "   └─ Laravel app: $([ -f "backend/artisan" ] && echo "✅ OK" || echo "❌ Missing artisan")"
    echo "   └─ Composer: $([ -f "backend/composer.json" ] && echo "✅ OK" || echo "❌ Missing composer.json")"
else
    echo "❌ Backend: Thiếu thư mục"
fi

# Crawl check  
if [ -d "crawl" ]; then
    crawl_files=$(find crawl -type f | wc -l)
    echo "✅ Crawl Service: $crawl_files files"
    echo "   └─ Node.js app: $([ -f "crawl/package.json" ] && echo "✅ OK" || echo "❌ Missing package.json")"
else
    echo "❌ Crawl Service: Thiếu thư mục"
fi

# Nginx check
if [ -d "nginx" ]; then
    nginx_files=$(find nginx -type f | wc -l)  
    echo "✅ Nginx: $nginx_files files"
    echo "   └─ Config: $([ -d "nginx/conf.d" ] && echo "✅ OK" || echo "❌ Missing conf.d")"
else
    echo "❌ Nginx: Thiếu thư mục"
fi

# PostgreSQL check
if [ -d "postgresql" ]; then
    postgres_files=$(find postgresql -type f | wc -l)
    echo "✅ PostgreSQL: $postgres_files files"
else
    echo "❌ PostgreSQL: Thiếu thư mục"
fi

# Docker check
echo ""
echo "🐳 Docker files:"
echo "   └─ docker-compose.yml: $([ -f "docker-compose.yml" ] && echo "✅ OK" || echo "❌ Missing")"
echo "   └─ .env.example: $([ -f ".env.example" ] && echo "✅ OK" || echo "❌ Missing")"

# Mobile/Frontend check
echo ""
echo "📱 Frontend files:"
echo "   └─ mobile/: $([ -d "mobile" ] && echo "✅ OK" || echo "❌ Missing")"
echo "   └─ admin/: $([ -d "admin" ] && echo "✅ OK" || echo "⚠️ Optional")"

echo ""
echo "💾 Dung lượng sử dụng:"
du -sh . 2>/dev/null
du -sh */ 2>/dev/null | sort -hr

echo ""
echo "🎯 Trạng thái: $([ -d "backend" ] && [ -d "crawl" ] && [ -d "nginx" ] && [ -d "postgresql" ] && echo "✅ SẴN SÀNG TRIỂN KHAI" || echo "⚠️ CẦN KIỂM TRA THÊM")"
EOF

chmod +x check_extraction.sh
./check_extraction.sh
```

### 📁 Giải Thích Về Thư Mục `home/`

Thư mục `home/` chứa các file và cấu hình production đã được tối ưu hóa:

#### Cấu Trúc Thư Mục `home/`:
```
home/
├── docker-compose.yml          # Docker Compose production config
├── .env.example               # Environment template đã tối ưu
├── .env.production            # Environment production (nếu có)
├── mobile/                    # Mobile Web App
│   ├── index.html            # Trang chính mobile
│   ├── css/                  # Stylesheets
│   ├── js/                   # JavaScript files
│   └── assets/               # Images, fonts
├── admin/                     # Admin Panel (nếu có)
│   ├── index.html            # Admin dashboard
│   ├── css/                  # Admin styles
│   └── js/                   # Admin scripts
├── api/                       # API Documentation
│   └── docs/                 # API docs
├── nginx/                     # Nginx production configs
│   └── conf.d/               # Site configurations
├── scripts/                   # Deployment scripts
│   ├── deploy.sh             # Auto deployment
│   ├── backup.sh             # Backup script
│   └── health-check.sh       # Health monitoring
└── configs/                   # Additional configs
    ├── php.ini               # PHP configuration
    ├── redis.conf            # Redis configuration
    └── postgresql.conf       # PostgreSQL tuning
```

#### Các File Quan Trọng Trong `home/`:

1. **docker-compose.yml** - Cấu hình production với:
   - Memory limits
   - Restart policies
   - Health checks
   - Volume mounts tối ưu

2. **mobile/index.html** - Mobile Web App với:
   - Responsive design
   - API integration
   - User authentication
   - Real-time updates

3. **.env.example** - Template environment với:
   - Production database settings
   - Security configurations
   - API endpoints
   - Cache settings

#### Lệnh Copy Chi Tiết:

```bash
# Copy toàn bộ nội dung từ home/ vào thư mục hiện tại
cp -r home/* ./

# Giải thích các tham số:
# -r : Copy recursively (bao gồm cả thư mục con)
# home/* : Tất cả files và folders trong home/
# ./ : Thư mục hiện tại (/var/www/bong88)
```

#### Các Lệnh Copy Thay Thế:

```bash
# Copy từng phần riêng biệt (nếu cần kiểm soát)
cp home/docker-compose.yml ./
cp home/.env.example ./
cp -r home/mobile ./
cp -r home/admin ./
cp -r home/nginx ./

# Copy với backup (nếu file đã tồn tại)
cp -r home/* ./ --backup=numbered

# Copy chỉ khi file mới hơn
cp -ru home/* ./
```
```

### Bước 2: Cấu Hình File Environment

```bash
# Kiểm tra file .env trong thư mục gốc dự án
ls -la /var/www/bong88/.env

# Nếu chưa có, tạo từ template
cp /var/www/bong88/.env.example /var/www/bong88/.env

# Chỉnh sửa cấu hình (quan trọng!)
nano /var/www/bong88/.env
```

### 📝 File `.env` - Đường Dẫn: `/var/www/bong88/.env`

Đây là file cấu hình chính của Laravel. **QUAN TRỌNG**: File này phải được đặt ở thư mục gốc của dự án:

```bash
# Vị trí file
/var/www/bong88/.env
```

### Nội Dung File `.env` Cần Chỉnh Sửa:

```env
# === CẤU HÌNH ỨNG DỤNG ===
APP_NAME=BONG88
APP_ENV=production
APP_KEY=base64:DVrVwYSL6z2KSwCpyeu50j5Gkhi2Cgjj2gjL0UMO0l0=
APP_DEBUG=false
APP_LOG_LEVEL=error
APP_URL=http://your-domain.com
TZ=Asia/Ho_Chi_Minh

# === BẢO TRÌ ===
APP_MAINTAIN=false
PERMISSION_ONLINE=admin,super

# === CƠ SỞ DỮ LIỆU ===
DB_CONNECTION=pgsql
DB_HOST=db
DB_PORT=5432
DB_DATABASE=bongda
DB_USERNAME=postgres
DB_PASSWORD=uN7s2P89cr3kV  # ĐỔI MẬT KHẨU MẠNH HƠN
DB_LOGGING=false

# === REDIS CACHE ===
REDIS_HOST=redis
REDIS_PASSWORD=RWzf@(V@Hps1+igQ5  # ĐỔI MẬT KHẨU MẠNH HƠN
REDIS_PORT=6379

# === API BÊN NGOÀI ===
APP_API=https://api.betsapi.com
APP_API_TOKEN=3869-hDrKmu1jsNQ3TC  # THAY ĐỔI TOKEN
APP_API_SOURCE=sbobet

# === BẢO MẬT ===
JWT_SECRET=DVrVwYSL6z2KSwCpyeu50j5Gkhi2Cgjj2gjL0UMO0l0  # TẠO MỚI

# === EMAIL (TÙY CHỌN) ===
MAIL_DRIVER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
```

### 🔧 Hướng Dẫn Tạo và Chỉnh Sửa File `.env`

#### Cách 1: Tạo từ Template
```bash
# Di chuyển vào thư mục dự án
cd /var/www/bong88

# Copy từ template (nếu có)
cp .env.example .env

# Chỉnh sửa file
nano .env
```

#### Cách 2: Tạo File Mới Hoàn Toàn
```bash
# Tạo file .env mới
cat > /var/www/bong88/.env << 'EOF'
# === CẤU HÌNH ỨNG DỤNG ===
APP_NAME=BONG88
APP_ENV=production
APP_KEY=base64:DVrVwYSL6z2KSwCpyeu50j5Gkhi2Cgjj2gjL0UMO0l0=
APP_DEBUG=false
APP_LOG_LEVEL=error
APP_URL=http://your-domain.com
TZ=Asia/Ho_Chi_Minh

# === BẢO TRÌ ===
APP_MAINTAIN=false
PERMISSION_ONLINE=admin,super

# === CƠ SỞ DỮ LIỆU ===
DB_CONNECTION=pgsql
DB_HOST=db
DB_PORT=5432
DB_DATABASE=bongda
DB_USERNAME=postgres
DB_PASSWORD=uN7s2P89cr3kV
DB_LOGGING=false

# === REDIS CACHE ===
REDIS_HOST=redis
REDIS_PASSWORD=RWzf@(V@Hps1+igQ5
REDIS_PORT=6379

# === API BÊN NGOÀI ===
APP_API=https://api.betsapi.com
APP_API_TOKEN=3869-hDrKmu1jsNQ3TC
APP_API_SOURCE=sbobet

# === BẢO MẬT ===
JWT_SECRET=DVrVwYSL6z2KSwCpyeu50j5Gkhi2Cgjj2gjL0UMO0l0

# === EMAIL (TÙY CHỌN) ===
MAIL_DRIVER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
EOF

echo "✅ File .env đã được tạo tại /var/www/bong88/.env"
```

#### Cách 3: Sử dụng Script Tự Động
```bash
# Tạo script để tự động tạo .env với thông tin cần thiết
cat > create_env.sh << 'EOF'
#!/bin/bash

echo "🔧 Tạo file .env cho BONG88"
echo "=========================="

# Hỏi thông tin cần thiết
read -p "Nhập domain của bạn (ví dụ: bong88.com): " DOMAIN
read -p "Nhập password cho PostgreSQL (mặc định: uN7s2P89cr3kV): " DB_PASS
DB_PASS=${DB_PASS:-uN7s2P89cr3kV}

read -p "Nhập password cho Redis (mặc định: RWzf@(V@Hps1+igQ5): " REDIS_PASS
REDIS_PASS=${REDIS_PASS:-"RWzf@(V@Hps1+igQ5"}

# Tạo APP_KEY mới
APP_KEY=$(openssl rand -base64 32)

# Tạo JWT_SECRET mới  
JWT_SECRET=$(openssl rand -base64 32)

# Tạo file .env
cat > .env << EOL
# === CẤU HÌNH ỨNG DỤNG ===
APP_NAME=BONG88
APP_ENV=production
APP_KEY=base64:$APP_KEY
APP_DEBUG=false
APP_LOG_LEVEL=error
APP_URL=http://$DOMAIN
TZ=Asia/Ho_Chi_Minh

# === BẢO TRÌ ===
APP_MAINTAIN=false
PERMISSION_ONLINE=admin,super

# === CƠ SỞ DỮ LIỆU ===
DB_CONNECTION=pgsql
DB_HOST=db
DB_PORT=5432
DB_DATABASE=bongda
DB_USERNAME=postgres
DB_PASSWORD=$DB_PASS
DB_LOGGING=false

# === REDIS CACHE ===
REDIS_HOST=redis
REDIS_PASSWORD=$REDIS_PASS
REDIS_PORT=6379

# === API BÊN NGOÀI ===
APP_API=https://api.betsapi.com
APP_API_TOKEN=3869-hDrKmu1jsNQ3TC
APP_API_SOURCE=sbobet

# === BẢO MẬT ===
JWT_SECRET=$JWT_SECRET

# === EMAIL (TÙY CHỌN) ===
MAIL_DRIVER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
EOL

echo "✅ File .env đã được tạo với APP_KEY và JWT_SECRET mới!"
echo "📍 Vị trí: $(pwd)/.env"
echo ""
echo "🔑 Thông tin quan trọng:"
echo "Domain: $DOMAIN"
echo "DB Password: $DB_PASS"
echo "Redis Password: $REDIS_PASS"
echo ""
echo "⚠️ Hãy backup và bảo mật các thông tin này!"
EOF

chmod +x create_env.sh
./create_env.sh
```

### 🔍 Kiểm Tra File `.env`

```bash
# Kiểm tra file đã tồn tại
ls -la /var/www/bong88/.env

# Xem nội dung file (che giấu password)
cat /var/www/bong88/.env | grep -v PASSWORD

# Kiểm tra quyền file (phải là 600 hoặc 644)
chmod 600 /var/www/bong88/.env

# Kiểm tra owner
chown $USER:$USER /var/www/bong88/.env

# Validate cấu hình (sau khi Docker đã chạy)
docker-compose exec backend php artisan config:show | head -20
```

### ⚠️ Lưu Ý Quan Trọng

1. **Vị Trí File**: File `.env` phải nằm ở `/var/www/bong88/.env` (thư mục gốc)
2. **Bảo Mật**: File này chứa thông tin nhạy cảm, không được commit vào Git
3. **Quyền File**: Nên set quyền 600 để chỉ owner có thể đọc/ghi
4. **Backup**: Luôn backup file này trước khi thay đổi

### 🎯 Các Thông Số Cần Thay Đổi Bắt Buộc:

- `APP_URL`: Thay `your-domain.com` bằng domain thật của bạn
- `DB_PASSWORD`: Đổi password database mạnh hơn
- `REDIS_PASSWORD`: Đổi password Redis mạnh hơn  
- `APP_API_TOKEN`: Thay token API thật (nếu có)
- `JWT_SECRET`: Tạo secret key mới cho JWT

### Bước 3: Cài Đặt và Cấu Hình Nginx Cơ Bản

#### 3.1 Cài Đặt Nginx Trên VPS (Host)

```bash
# Cài đặt Nginx
sudo apt update
sudo apt install nginx -y

# Khởi động và enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Kiểm tra trạng thái
sudo systemctl status nginx

# Kiểm tra Nginx đang chạy
curl http://localhost
```

#### 3.2 Cấu Hình Nginx Reverse Proxy

Tạo file cấu hình cho domain:

```bash
# Tạo file cấu hình site
sudo nano /etc/nginx/sites-available/bong88
```

**Nội dung file `/etc/nginx/sites-available/bong88`:**

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    # Logs
    access_log /var/log/nginx/bong88_access.log;
    error_log /var/log/nginx/bong88_error.log;

    # Reverse proxy tới Docker container
    location / {
        proxy_pass http://127.0.0.1:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

#### 3.3 Kích Hoạt Site

```bash
# Kích hoạt site
sudo ln -s /etc/nginx/sites-available/bong88 /etc/nginx/sites-enabled/

# Xóa default site
sudo rm /etc/nginx/sites-enabled/default

# Test cấu hình
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

#### 3.4 Cấu Hình Nginx Trong Container

Kiểm tra và tạo cấu hình Nginx cho container:

```bash
# Kiểm tra thư mục nginx
ls -la nginx/conf.d/

# Tạo thư mục nếu chưa có
mkdir -p nginx/conf.d
```

**Tạo file `nginx/conf.d/app.conf` (cho container) - Đã sửa lỗi:**

```bash
cat > nginx/conf.d/app.conf << 'EOF'
server {
    listen 80;
    server_name localhost;
    root /var/www/public;
    index index.php index.html;

    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    client_max_body_size 50M;
    
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location /api/ {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location /portal/ {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass backend:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    location ~ /\. {
        deny all;
    }

    location ~ /(storage|bootstrap|config|database|resources|routes|tests|vendor)/ {
        deny all;
    }

    location ~ /\.env {
        deny all;
    }
}
EOF
```

### 🔍 Kiểm Tra Cấu Hình Nginx

```bash
# Kiểm tra file đã tạo đúng
cat nginx/conf.d/app.conf

# Validate syntax Nginx (khi container đã chạy)
docker-compose exec nginx nginx -t

# Nếu có lỗi syntax, kiểm tra lại
docker-compose exec nginx nginx -T | grep -A 20 -B 5 "error"
```

### 📝 Cấu Hình Nginx Cực Kỳ Đơn Giản (Nếu Vẫn Lỗi)

```bash
# Tạo file cấu hình đơn giản nhất có thể
cat > nginx/conf.d/simple.conf << 'EOF'
server {
    listen 80;
    server_name localhost;
    root /var/www/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass backend:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
EOF

echo "✅ File simple.conf đã được tạo"
```

### 🛠️ Script Tạo Nginx Config An Toàn

```bash
# Script tạo config nginx với kiểm tra lỗi
cat > create_nginx_config.sh << 'EOF'
#!/bin/bash

echo "🔧 TẠO CẤU HÌNH NGINX AN TOÀN"
echo "============================="

# Tạo thư mục nếu chưa có
mkdir -p nginx/conf.d

# Xóa file cũ
rm -f nginx/conf.d/*.conf

echo "📝 Tạo cấu hình đơn giản..."

# Tạo file cấu hình cơ bản
cat > nginx/conf.d/default.conf << 'NGINXCONF'
server {
    listen 80;
    server_name localhost;
    root /var/www/public;
    index index.php index.html;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass backend:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    location ~ /\. {
        deny all;
    }
}
NGINXCONF

echo "✅ Cấu hình nginx đã được tạo: nginx/conf.d/default.conf"

# Kiểm tra file
echo "📄 Nội dung file:"
cat nginx/conf.d/default.conf

echo ""
echo "🧪 Để test cấu hình, chạy lệnh sau khi Docker đã khởi động:"
echo "docker-compose exec nginx nginx -t"
EOF

chmod +x create_nginx_config.sh
./create_nginx_config.sh
```

### 📝 File Nginx Alternative (Nếu cần)

Nếu bạn muốn tạo file cấu hình khác, đây là template khác:

```bash
# Tạo file default.conf thay vì app.conf
cat > nginx/conf.d/default.conf << 'EOF'
server {
    listen 80 default_server;
    server_name _;
    root /var/www/public;
    index index.php index.html;

    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;

    # Logs
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # Main location for Laravel
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # API endpoints
    location /api/ {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Admin portal
    location /portal/ {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Mobile app
    location /mobile/ {
        try_files $uri $uri/ /mobile/index.html;
    }

    # PHP-FPM processing
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass backend:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        include fastcgi_params;
        
        # Timeout settings
        fastcgi_connect_timeout 60;
        fastcgi_send_timeout 180;
        fastcgi_read_timeout 180;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
    }

    # Static files with caching
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|eot|svg|pdf|zip)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
        try_files $uri =404;
    }

    # Security: Deny access to hidden files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Security: Deny access to Laravel sensitive directories
    location ~ /(storage|bootstrap|config|database|resources|routes|tests|vendor)/ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Security: Deny access to .env and other config files
    location ~ /\.(env|htaccess|gitignore|gitattributes)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        application/xml
        image/svg+xml;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # Limit request size (for file uploads)
    client_max_body_size 50M;
    client_body_buffer_size 128k;
}
EOF
```

#### 3.5 Xử Lý Lỗi Nginx (Troubleshooting)

**🚨 Nếu gặp lỗi: "Job for nginx.service failed"**

```bash
# 1. Kiểm tra trạng thái chi tiết
sudo systemctl status nginx.service

# 2. Xem logs lỗi chi tiết
sudo journalctl -xeu nginx.service

# 3. Kiểm tra syntax cấu hình
sudo nginx -t

# 4. Kiểm tra port đang được sử dụng
sudo netstat -tulpn | grep :80
sudo lsof -i :80

# 5. Kill process đang dùng port 80 (nếu có)
sudo fuser -k 80/tcp

# 6. Kiểm tra quyền file cấu hình
sudo ls -la /etc/nginx/sites-enabled/
sudo ls -la /etc/nginx/sites-available/

# 7. Xóa cấu hình lỗi và tạo lại
sudo rm /etc/nginx/sites-enabled/bong88
sudo rm /etc/nginx/sites-available/bong88

# 8. Tạo lại cấu hình đơn giản
sudo tee /etc/nginx/sites-available/bong88 << 'EOF'
server {
    listen 80;
    server_name _;
    
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# 9. Kích hoạt lại site
sudo ln -s /etc/nginx/sites-available/bong88 /etc/nginx/sites-enabled/

# 10. Test cấu hình
sudo nginx -t

# 11. Restart nginx
sudo systemctl restart nginx

# 12. Kiểm tra trạng thái
sudo systemctl status nginx
```

**🔧 Script Tự Động Sửa Lỗi Nginx (Phiên bản sửa lỗi):**

```bash
# Tạo script sửa lỗi nginx đã được kiểm tra kỹ
cat > fix_nginx.sh << 'EOF'
#!/bin/bash

echo "🔧 SCRIPT SỬA LỗI NGINX - PHIÊN BẢN SỬA LỖI"
echo "============================================="

# Stop nginx nếu đang chạy
echo "🛑 Dừng nginx..."
sudo systemctl stop nginx 2>/dev/null

# Kill tất cả process nginx
echo "💀 Kill tất cả process nginx..."
sudo pkill -f nginx 2>/dev/null
sudo fuser -k 80/tcp 2>/dev/null

# Kiểm tra ai đang dùng port 80
echo "🔍 Kiểm tra port 80..."
if sudo lsof -i :80 >/dev/null 2>&1; then
    echo "⚠️ Port 80 vẫn đang được sử dụng:"
    sudo lsof -i :80
    echo "💀 Killing processes on port 80..."
    sudo lsof -ti :80 | xargs sudo kill -9 2>/dev/null
fi

# Xóa cấu hình cũ
echo "🗑️ Xóa cấu hình cũ..."
sudo rm -f /etc/nginx/sites-enabled/* 2>/dev/null
sudo rm -f /etc/nginx/sites-available/bong88 2>/dev/null

# Backup cấu hình gốc
echo "💾 Backup cấu hình nginx..."
sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup 2>/dev/null

# Tạo cấu hình mới đơn giản và chính xác
echo "📝 Tạo cấu hình mới..."
sudo tee /etc/nginx/sites-available/bong88 > /dev/null << 'EOL'
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name _;
    
    server_tokens off;
    
    access_log /var/log/nginx/bong88_access.log;
    error_log /var/log/nginx/bong88_error.log;
    
    location /health {
        access_log off;
        return 200 "OK\n";
        add_header Content-Type text/plain;
    }
    
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        error_page 502 503 504 /50x.html;
    }
    
    location = /50x.html {
        root /var/www/html;
        internal;
    }
    
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}
EOL

# Tạo error page
echo "📄 Tạo error page..."
sudo mkdir -p /var/www/html
sudo tee /var/www/html/50x.html > /dev/null << 'EOL'
<!DOCTYPE html>
<html>
<head>
    <title>Service Starting</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
        .container { max-width: 600px; margin: 0 auto; }
        h1 { color: #333; }
        p { color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Service Starting Up</h1>
        <p>The backend service is starting. Please wait a moment and refresh the page.</p>
        <p><small>If this problem persists, please contact the administrator.</small></p>
    </div>
</body>
</html>
EOL

# Kích hoạt site
echo "🔗 Kích hoạt site..."
sudo ln -s /etc/nginx/sites-available/bong88 /etc/nginx/sites-enabled/bong88

# Test cấu hình
echo "🧪 Test cấu hình nginx..."
if sudo nginx -t; then
    echo "✅ Cấu hình nginx OK"
else
    echo "❌ Cấu hình nginx có lỗi:"
    sudo nginx -t
    echo ""
    echo "🔧 Đang tạo cấu hình backup đơn giản hơn..."
    
    # Tạo cấu hình backup cực kỳ đơn giản
    sudo rm -f /etc/nginx/sites-available/bong88
    sudo tee /etc/nginx/sites-available/bong88 > /dev/null << 'SIMPLE'
server {
    listen 80;
    server_name _;
    
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
    }
    
    location /health {
        return 200 "OK";
        add_header Content-Type text/plain;
    }
}
SIMPLE
    
    echo "🧪 Test lại cấu hình đơn giản..."
    if ! sudo nginx -t; then
        echo "❌ Vẫn có lỗi, đang khôi phục cấu hình mặc định..."
        sudo rm -f /etc/nginx/sites-available/bong88
        sudo rm -f /etc/nginx/sites-enabled/bong88
        exit 1
    fi
fi

# Start nginx
echo "🚀 Khởi động nginx..."
if sudo systemctl start nginx; then
    echo "✅ Nginx đã khởi động thành công"
else
    echo "❌ Nginx không thể khởi động:"
    sudo systemctl status nginx --no-pager
    sudo journalctl -xeu nginx.service --no-pager -n 20
    exit 1
fi

# Enable nginx
sudo systemctl enable nginx

# Kiểm tra trạng thái
echo "📊 Kiểm tra trạng thái..."
sudo systemctl status nginx --no-pager

# Test HTTP
echo "🌐 Test HTTP response..."
if curl -s http://localhost/health >/dev/null 2>&1; then
    echo "✅ HTTP response OK"
else
    echo "⚠️ Backend chưa sẵn sàng (bình thường nếu Docker chưa chạy)"
fi

echo ""
echo "🎉 Hoàn thành! Nginx đã được cấu hình."
echo "💡 File cấu hình: /etc/nginx/sites-available/bong88"
echo "📝 Logs: /var/log/nginx/bong88_access.log"
echo "📝 Error logs: /var/log/nginx/bong88_error.log"
echo ""
echo "🐳 Bây giờ bạn có thể chạy Docker containers với port mapping:"
echo "   - Nginx container: 8080:80"
echo "   - Host nginx: proxy từ 80 -> 8080"
EOF

chmod +x fix_nginx.sh
./fix_nginx.sh
```

**🔧 Script Kiểm Tra Nginx Sau Khi Sửa:**

```bash
# Script kiểm tra chi tiết nginx
cat > check_nginx.sh << 'EOF'
#!/bin/bash

echo "🔍 KIỂM TRA NGINX CONFIGURATION"
echo "==============================="

echo "📊 Trạng thái Nginx:"
sudo systemctl status nginx --no-pager

echo ""
echo "🧪 Test cấu hình:"
sudo nginx -t

echo ""
echo "📁 Files cấu hình:"
echo "Sites available:"
sudo ls -la /etc/nginx/sites-available/
echo ""
echo "Sites enabled:"
sudo ls -la /etc/nginx/sites-enabled/

echo ""
echo "📄 Nội dung cấu hình:"
if [ -f "/etc/nginx/sites-available/bong88" ]; then
    echo "--- /etc/nginx/sites-available/bong88 ---"
    sudo cat /etc/nginx/sites-available/bong88
else
    echo "❌ File cấu hình không tồn tại"
fi

echo ""
echo "🌐 Test HTTP:"
echo "Health check:"
curl -s http://localhost/health || echo "❌ Health check failed"

echo ""
echo "🔍 Port listening:"
sudo netstat -tulpn | grep :80

echo ""
echo "📝 Logs gần đây:"
if [ -f "/var/log/nginx/error.log" ]; then
    echo "--- Error logs (5 dòng cuối) ---"
    sudo tail -5 /var/log/nginx/error.log
fi

if [ -f "/var/log/nginx/bong88_error.log" ]; then
    echo "--- Bong88 error logs (5 dòng cuối) ---"
    sudo tail -5 /var/log/nginx/bong88_error.log
fi
EOF

chmod +x check_nginx.sh
```

**📝 Cấu Hình Docker Compose Để Tránh Conflict:**

```bash
# Sửa docker-compose.yml để nginx container dùng port 8080
cat > docker-compose-fix.yml << 'EOF'
version: '3.8'

services:
  backend:
    container_name: backend
    restart: unless-stopped
    build:
      context: ./backend
      dockerfile: app.Dockerfile
    env_file:
      - ./.env
    tty: true
    working_dir: /var/www
    volumes:
      - ./backend/:/var/www
    networks:
      - 'external'

  db:
    container_name: postgres
    image: postgres:10.1-alpine
    restart: unless-stopped
    environment:
      POSTGRES_PASSWORD: uN7s2P89cr3kV
      POSTGRES_USER: postgres
      POSTGRES_DB: bongda
    expose:
      - 5432
    volumes:
      - ./postgresql:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - 'external'

  nginx:
    container_name: nginx
    image: nginx:alpine
    restart: unless-stopped
    tty: true
    ports:
      - "8080:80"  # Thay đổi từ 80:80 thành 8080:80
    volumes:
      - ./backend/:/var/www
      - ./nginx/conf.d/:/etc/nginx/conf.d/
    networks:
      - 'external'

  crawl:
    container_name: crawl
    build:
      context: ./crawl
      dockerfile: Dockerfile
    restart: unless-stopped
    env_file:
      - ./.env
    networks:
      - 'external'

  redis:
    container_name: redis
    image: redis
    command: --requirepass "RWzf@(V@Hps1+igQ5"
    expose:
      - 6379
    networks:
      - 'external'

networks: 
  external: 
    name: api-system

volumes:
  dbdata:
    driver: local
EOF

echo "✅ File docker-compose-fix.yml đã được tạo"
echo "💡 Sử dụng: docker-compose -f docker-compose-fix.yml up -d"
```

**🔍 Debug Commands:**

```bash
# Kiểm tra chi tiết lỗi
sudo journalctl -xeu nginx.service --no-pager | tail -20

# Kiểm tra tất cả port đang sử dụng
sudo netstat -tulpn | grep LISTEN

# Kiểm tra process nginx
ps aux | grep nginx

# Kiểm tra file cấu hình có đúng syntax không
sudo nginx -T

# Xem log realtime
sudo tail -f /var/log/nginx/error.log
```

#### 3.6 Cấu Hình Firewall

```bash
# Mở port HTTP và HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 22/tcp

# Kiểm tra status
sudo ufw status

# Enable firewall (nếu chưa)
sudo ufw enable
```

---

## 🐳 Triển Khai Hệ Thống

### Bước 1: Tạo Docker Network

```bash
# Tạo network để các container giao tiếp
docker network create api-system
```

### Bước 2: Kiểm Tra Docker Compose

```bash
# Kiểm tra file docker-compose.yml
cat docker-compose.yml

# Validate cấu hình
docker-compose config
```

### Bước 3: Build và Chạy Hệ Thống

```bash
# Build images (lần đầu tiên hoặc khi có thay đổi code)
docker-compose build

# Chạy tất cả services ở background
docker-compose up -d

# Kiểm tra trạng thái containers
docker-compose ps

# Xem logs
docker-compose logs -f
```

### Bước 4: Thiết Lập Database

```bash
# Chờ PostgreSQL khởi động hoàn toàn (khoảng 30 giây)
sleep 30

# Chạy migrations
docker-compose exec backend php artisan migrate

# Chạy seeders để tạo dữ liệu mẫu
docker-compose exec backend php artisan db:seed

# Hoặc chỉ chạy seeder admin
docker-compose exec backend php artisan db:seed --class=AdminUserSeeder

# Generate application key (nếu cần)
docker-compose exec backend php artisan key:generate

# Clear cache
docker-compose exec backend php artisan cache:clear
docker-compose exec backend php artisan config:clear
docker-compose exec backend php artisan route:clear
```

### Bước 5: Thiết Lập Quyền File

```bash
# Vào container backend
docker-compose exec backend bash

# Thiết lập quyền (trong container)
chown -R www-data:www-data /var/www/storage
chown -R www-data:www-data /var/www/bootstrap/cache
chmod -R 775 /var/www/storage
chmod -R 775 /var/www/bootstrap/cache

# Thoát container
exit
```

---

## 🔐 Thông Tin Đăng Nhập

### Tài Khoản Admin Chính

| Thông Tin | Giá Trị |
|-----------|---------|
| **Username** | `ADMIN` |
| **Password** | `bongda@123` |
| **Loại** | Super Admin |
| **Wallet** | 1,000,000,000 |
| **URL Đăng Nhập** | `http://your-domain.com` |

### Tài Khoản Khác

| Username | Password | Loại | Mô Tả |
|----------|----------|------|-------|
| `B0` | `123456` | Super | Tài khoản Super |
| `B000` | `123456` | Master | Tài khoản Master |
| `B00000` | `123456` | Agent | Tài khoản Agent |
| `B0000000` | `123456` | Member | Tài khoản Member |

### Database Access

| Service | Host | Port | Database | Username | Password |
|---------|------|------|----------|----------|----------|
| PostgreSQL | `localhost` | `5432` | `bongda` | `postgres` | `uN7s2P89cr3kV` |
| Redis | `localhost` | `6379` | - | - | `RWzf@(V@Hps1+igQ5` |

---

## 🛠️ Quản Lý Hệ Thống

### Các Lệnh Docker Compose Cơ Bản

```bash
# Xem trạng thái containers
docker-compose ps

# Xem logs realtime
docker-compose logs -f

# Xem logs của service cụ thể
docker-compose logs backend
docker-compose logs crawl
docker-compose logs db
docker-compose logs nginx
docker-compose logs redis

# Restart toàn bộ hệ thống
docker-compose restart

# Restart service cụ thể
docker-compose restart backend
docker-compose restart nginx

# Dừng hệ thống
docker-compose stop

# Dừng và xóa containers (giữ lại volumes/data)
docker-compose down

# Dừng và xóa tất cả (bao gồm volumes - CẨN THẬN!)
docker-compose down -v

# Update code và restart
git pull
docker-compose build
docker-compose up -d
```

### Backup Database

```bash
# Backup database
docker-compose exec db pg_dump -U postgres bongda > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore database
docker-compose exec -T db psql -U postgres bongda < backup_file.sql
```

### Monitoring & Logs

```bash
# Xem resource usage
docker stats

# Xem logs Laravel
docker-compose exec backend tail -f storage/logs/laravel.log

# Xem logs Nginx access
docker-compose exec nginx tail -f /var/log/nginx/access.log

# Xem logs Nginx error
docker-compose exec nginx tail -f /var/log/nginx/error.log
```

### Laravel Artisan Commands

```bash
# Vào container backend
docker-compose exec backend bash

# Chạy migrations
php artisan migrate

# Rollback migrations
php artisan migrate:rollback

# Seed database
php artisan db:seed

# Clear cache
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Queue work (nếu sử dụng queue)
php artisan queue:work

# Tạo user mới
php artisan tinker
# Trong tinker:
# $user = new App\Models\User();
# $user->username = 'newuser';
# $user->setPassword('password');
# $user->save();
```

---

## 🔧 Troubleshooting

### Lỗi Thường Gặp

#### 1. Container không khởi động được

```bash
# Kiểm tra logs
docker-compose logs [service_name]

# Kiểm tra port conflict
sudo netstat -tulpn | grep :[port]

# Kill process đang dùng port
sudo kill -9 [PID]
```

#### 2. Database connection failed

```bash
# Kiểm tra PostgreSQL đang chạy
docker-compose exec db pg_isready -U postgres

# Kiểm tra kết nối từ backend
docker-compose exec backend ping db

# Reset database
docker-compose down
docker volume rm bong88_dbdata
docker-compose up -d
```

#### 3. Permission denied errors

```bash
# Fix quyền storage
docker-compose exec backend chown -R www-data:www-data storage
docker-compose exec backend chmod -R 775 storage

# Fix quyền cache
docker-compose exec backend chown -R www-data:www-data bootstrap/cache
docker-compose exec backend chmod -R 775 bootstrap/cache
```

#### 4. 502 Bad Gateway (Nginx)

```bash
# Kiểm tra backend container
docker-compose ps backend

# Restart nginx
docker-compose restart nginx

# Kiểm tra nginx config
docker-compose exec nginx nginx -t
```

#### 5. Memory issues

```bash
# Tăng memory cho containers trong docker-compose.yml
services:
  backend:
    mem_limit: 1g
  db:
    mem_limit: 2g
```

### Debug Commands

```bash
# Vào container để debug
docker-compose exec backend bash
docker-compose exec db psql -U postgres bongda

# Kiểm tra network
docker network ls
docker network inspect api-system

# Xem Docker system info
docker system df
docker system prune  # Dọn dẹp (cẩn thận!)
```

---

## 🛡️ Bảo Mật & Tối Ưu

### Bảo Mật

#### 1. Thay Đổi Passwords Mặc Định

```bash
# Database password
# Sửa trong .env và docker-compose.yml

# Redis password
# Sửa trong .env

# Admin passwords
# Đăng nhập và đổi trong interface admin
```

#### 2. Firewall Configuration

```bash
# Cài đặt UFW
sudo ufw enable

# Chỉ mở port cần thiết
sudo ufw allow 22      # SSH
sudo ufw allow 80      # HTTP
sudo ufw allow 443     # HTTPS (nếu có SSL)

# Chặn direct access vào database
sudo ufw deny 5432
sudo ufw deny 6379
```

#### 3. SSL Certificate (Khuyến Nghị)

```bash
# Cài đặt Certbot
sudo apt install certbot python3-certbot-nginx

# Lấy SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Thêm dòng: 0 12 * * * /usr/bin/certbot renew --quiet
```


# Chạy health check mỗi 5 phút
crontab -e
# Thêm: */5 * * * * /home/<USER>/var/log/domain_health.log
```

### URLs Cuối Cùng Sau Khi Cấu Hình:

- **🏠 Trang Chính**: `https://your-domain.com` hoặc `https://www.your-domain.com`
- **⚙️ Admin Panel**: `https://admin.your-domain.com`
- **🔌 API Endpoint**: `https://api.your-domain.com/v1`
- **📱 Mobile App**: `https://mobile.your-domain.com`
- **📊 Health Check**: `https://api.your-domain.com/health`

