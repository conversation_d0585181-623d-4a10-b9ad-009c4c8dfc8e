#!/bin/bash

echo "🚑 SỬA LỖI DEBIAN BUSTER 404 - QUICKFIX"
echo "======================================="

# Di chuyển về thư mục backend
cd /var/www/bong88/backend || cd backend || { echo "❌ Không tìm thấy thư mục backend"; exit 1; }

# Backup Dockerfile cũ
if [ -f "app.Dockerfile" ]; then
    mv app.Dockerfile app.Dockerfile.backup
    echo "📋 Đã backup app.Dockerfile cũ"
fi

# Tạo Dockerfile mới với PHP 7.4
echo "📝 Tạo app.Dockerfile mới với PHP 7.4..."
cat > app.Dockerfile << 'EOF'
FROM php:7.4-fpm

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpng-dev \
    libjpeg62-turbo-dev \
    libfreetype6-dev \
    locales \
    libpq-dev \
    libzip-dev \
    zip \
    jpegoptim optipng pngquant gifsicle \
    vim \
    unzip \
    git \
    curl \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-configure pgsql -with-pgsql=/usr/local/pgsql \
    && docker-php-ext-install pdo pdo_pgsql pgsql mbstring exif pcntl bcmath gd zip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www

# Copy composer files first for better caching
COPY composer.json composer.lock* ./

# Install PHP dependencies
RUN composer install --no-dev --no-scripts --no-autoloader --prefer-dist

# Copy application files
COPY . .

# Complete composer installation
RUN composer dump-autoload --no-dev --optimize

# Create directories and set permissions
RUN mkdir -p storage/logs storage/app storage/framework/cache storage/framework/sessions storage/framework/views \
    && mkdir -p bootstrap/cache \
    && chown -R www-data:www-data /var/www/storage /var/www/bootstrap/cache \
    && chmod -R 775 /var/www/storage /var/www/bootstrap/cache

# Expose port 9000
EXPOSE 9000

# Start PHP-FPM
CMD ["php-fpm"]
EOF

echo "✅ Dockerfile mới đã được tạo với PHP 7.4"

# Quay về thư mục chính
cd ..

# Build lại container
echo "🔨 Build lại container với Dockerfile mới..."
docker-compose build webapp --no-cache

echo ""
echo "✅ Sửa lỗi hoàn thành!"
echo "🚀 Bây giờ chạy: docker-compose up -d"
